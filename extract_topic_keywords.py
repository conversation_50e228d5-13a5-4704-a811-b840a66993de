# 请将以下代码复制到您的Jupyter notebook新单元格中运行

import pandas as pd
import os
import re

# 您的文本数据
text_data = """
【主题 0】对应的关键词：
转发, 哈哈哈, 别跑, 打得, 打死, 杀死, 打打, 过份, 找死, 迟早

【主题 1】对应的关键词：
repost, dtjhcfuj, poor, , , , , , , 

【主题 3】对应的关键词：
社会, 流泪, 制造, 现在, 报复, 祈祷, 发怒, 百态, 老百姓, 黑社会

【主题 4】对应的关键词：
流泪, 不易, 生路, 为难, 祈祷, 生活, 老百姓, 何必, 别人, 发怒

【主题 262】对应的关键词：
猛犸, 等待, a6ernrqb, 公安机关, 触犯, http, cn, 执法局, 广东, 打架

【主题 6】对应的关键词：
转发, 轉發, 匈牙利, , , , , , , 

【主题 7】对应的关键词：
捂脸, 犯法, 祈祷, 违法, 文明执法, 发怒, 法律, 什么, 老百姓, 依规

【主题 8】对应的关键词：
贪官, 几千万, 只想, 死刑, 百姓, icon, 不用, 绝路, 劝离, 刑拘

【主题 9】对应的关键词：
法律, 哪条, 执法, 下行, 符合规定, 处罚, 公众, 旗号, 对立, 社会秩序

【主题 13】对应的关键词：
灵光, 一闪, 打脸, 捂脸, 发怒, 曝光, 黑线, 光脚, 光天化日, 穿鞋

【主题 271】对应的关键词：
雪饼, 旺旺, 跨域, http, cn, 15, 大喊, a6ervqcl, 发布, 10

【主题 16】对应的关键词：
韦某, 进行, 执法人员, 事件, 队员, 天河区, 劝导, 环境, 加强, 摆卖

【主题 145】对应的关键词：
伤情, 卢某, 接报, 天河区, 到场, 34, 44, 警情, 庄某, 处置

【主题 15】对应的关键词：
处罚, 躲过, 结果, 持棍, 这次, 社会秩序, 出来, 法律, 行为, 提醒

【主题 147】对应的关键词：
警权, 怪物, 志海, 催生, 几千年, 解散, 扩大, 杜子建, 取消, 贩夫走卒

【主题 149】对应的关键词：
逃跑, 摔倒, 大喊, 流动, 不活, 广州, 铁棍, 城管, 倒退, 乌烟瘴气

【主题 150】对应的关键词：
后持, 打人者, 无证, 控制, 警方, 经营, 广州, a6egqykz, 网传, 浏览器

【主题 23】对应的关键词：
捂脸, 摆地摊, 地步, 地痞流氓, 地方, 为什么, 老百姓, 别人, 鸡毛, 地痞

【主题 29】对应的关键词：
冲突, 这场, 街头, 背后, 秩序, 来源于, 地摊, 图片, 爆发, 一个

【主题 31】对应的关键词：
禅语, 二字, 红色, 编辑, 祝福语, 灵验, 粘贴, 第一眼, 复制, 可见

【主题 38】对应的关键词：
白衣, 工作人员, 大皖, 或拉着, 侦办, 新闻, 图源, 全文如下, 16, 持棍

【主题 40】对应的关键词：
百态, 殴打, 热点, 热点新闻, 广州, 网传, 社会, 爱车, 话题, 王嘉昌

【主题 171】对应的关键词：
游商, 目击者, 比较, 劝阻, 带走, 激动, 情绪, 铁棍, 广州, 本地

【主题 48】对应的关键词：
账号, 咱们, 作品, 修改, 事儿, 超级, 反派, 大哥, 大爷, 视频

【主题 53】对应的关键词：
小商贩, 收摊, 难道, 规矩, 不服, 全国, 赶尽杀绝, 汉人, 地摊, 耍流氓

【主题 54】对应的关键词：
网传, 并持, 大喊, http, cn, 不活, a6egcb6f, 广州, 铁棍, 冲突

【主题 182】对应的关键词：
发生冲突, 大喊, 并持, 还举, 不活, 该不该, 狗迫, 很穷, 尽处, a6ed8vjj

【主题 200】对应的关键词：
老张, 城管局, 流动, 大家, 事情, 秘密, 蒙面人, 乐山, 队员, 查处

【主题 72】对应的关键词：
并持, 发生冲突, 大喊, 男子, 不活, 铁棍, 城管, 老白姓, 猛追, 另有隐情

【主题 87】对应的关键词：
16, 伤情, 卢某, 冲上, 接报, 天河区, 10, 到场, 工作人员, 日晚

【主题 91】对应的关键词：
老李, 摊主, 那天, 大哥, 一场, 队员, 规则, 小张, 心里, 每天

【主题 359】对应的关键词：
打架, 炸锅, 纠纷, 后续, 触犯, 官方, 评论, 行为, 法律, 广州

【主题 107】对应的关键词：
记者, 打人者, 无证, 事发, 骨折, 沙河, 获悉, 核实, 多名, 位于

【主题 364】对应的关键词：
并持, 激动, 大喊, 南阳, 律师, 不活, 大王, 情绪, 过分, 南京

【主题 249】对应的关键词：
货箱, 执法人员, 拦阻, 韦某, 放手, 棍子, 名城, 大部分, 唾沫, 水淹七军
"""

print("🚀 开始提取主题编号和关键词...")

# 使用正则表达式提取主题编号和关键词
pattern = r'【主题 (\d+)】对应的关键词：\s*\n([^\n【]+)'
matches = re.findall(pattern, text_data)

# 创建表格数据
table_data = []

for topic_num, keywords in matches:
    # 清理关键词，去除多余的空格和空值
    keywords_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
    keywords_clean = ', '.join(keywords_list)
    
    table_data.append({
        '主题编号': int(topic_num),
        '关键词': keywords_clean
    })

# 创建DataFrame并按主题编号排序
df_topics = pd.DataFrame(table_data)
df_topics = df_topics.sort_values('主题编号').reset_index(drop=True)

print(f"📊 成功提取 {len(df_topics)} 个主题")

# 创建桌面主题建模文件夹
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
os.makedirs(desktop_path, exist_ok=True)

# 保存到Excel文件
excel_path = os.path.join(desktop_path, '主题词和主题对应.xlsx')
df_topics.to_excel(excel_path, index=False)

print(f"📁 表格已保存到: {excel_path}")

# 显示表格
print("\n=== 主题词和主题对应表格 ===")
print(df_topics.to_string())

# 统计信息
print(f"\n📈 统计信息:")
print(f"   - 主题总数: {len(df_topics)}")
print(f"   - 主题编号范围: {df_topics['主题编号'].min()} - {df_topics['主题编号'].max()}")
print(f"   - 平均关键词长度: {df_topics['关键词'].str.len().mean():.1f} 字符")

print("\n🎉 主题词和主题对应表格已成功生成并保存到桌面的主题建模文件夹内！")
