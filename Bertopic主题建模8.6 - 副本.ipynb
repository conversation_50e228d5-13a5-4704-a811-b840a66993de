import pandas as pd
df = pd.read_csv('1_带主题.csv', encoding='utf-8-sig')

import jieba

def chinese_tokenizer(text):
    return " ".join(jieba.cut(text))

df["text_seg"] = df["全文内容"].apply(chinese_tokenizer)


#1、处理中文：使用适配中文的嵌入模型

from bertopic import BERTopic
from sentence_transformers import SentenceTransformer

# 使用 BERT 中文模型（推荐“paraphrase-multilingual-MiniLM”）
embedding_model = SentenceTransformer("paraphrase-multilingual-MiniLM-L12-v2")


# 2、创建主题模型并训练
# 创建 BERTopic 模型
topic_model = BERTopic(
    embedding_model=embedding_model,
    language="chinese",
    min_topic_size=20,  # 每个主题至少20条，减少碎片化
    verbose=True
)

# 拟合数据，提取主题
topics, probs = topic_model.fit_transform(df["text_seg"])
df["topic"] = topics

print(topic_model.get_topic_info())

# 获取所有主题的关键词字典
topic_keywords= {
    topic_num: ", ".join([word for word, _ in topic_model.get_topic(topic_num)])
    for topic_num in set(topics) if topic_num != -1  # -1 是异常主题
}
df['keywords'] = df['topic'].map(topic_keywords)
df.to_csv("1_带主题.csv", index=False, encoding='utf-8-sig')

#5、按用户统计主题占比
# 统计每个用户的主题出现次数
group_topic_counts = df.groupby(["用户类型", "topic"]).size().unstack(fill_value=0)

# 转换为百分比（每类用户的主题占比）
group_topic_percent = group_topic_counts.div(group_topic_counts.sum(axis=1), axis=0)

print(group_topic_percent)


# 8️⃣ 可选：输出每个主题的关键词
for tid in set(topics):
    print(f"\n📌 主题 {tid}：", topic_model.get_topic(tid))

df=pd.read_csv("1_带主题.csv", encoding='utf-8-sig')
df

# 确保你的时间字段是 datetime 类型
df['时间'] = pd.to_datetime(df['日期'])

# 提取小时（或其他时间段）
df['小时'] = df['时间'].dt.floor('h')  # 向下取整到小时


 #5、统计每小时的发帖数量（即声量）
hourly_volume = df.groupby('小时').size().reset_index(name='发帖数')
#6. 提取“天”字段
hourly_volume['天'] = hourly_volume['小时'].dt.date

#7、找出“总发帖数”最多的几天（比如Top 3）
# 每天总发帖数
daily_volume = (
    hourly_volume.groupby('天')['发帖数']
    .sum()
    .sort_values(ascending=False)
)

# top1 发帖量
top1_volume = daily_volume.iloc[0]

# 设置阈值（20%）
threshold = top1_volume * 0.1

# 选出高活跃的天（发帖量 ≥ 20% * top1）
top_days = daily_volume[daily_volume >= threshold]
print("最活跃的几天：")
print(top_days)

# 8. 只保留这几天的数据
活跃天列表 = top_days.index
活跃时段 = hourly_volume[hourly_volume['天'].isin(活跃天列表)]

#9.1找到峰值点
from scipy.signal import find_peaks,peak_widths
发帖数序列 = 活跃时段['发帖数'].values

peaks, properties = find_peaks(
    发帖数序列,
    height=发帖数序列.max()*0.2,      # 峰值本身高度至少最高峰值的20%
    distance=2      # 相邻峰最少间隔2个小时
)

#9.2 获取第一个峰和最后一个峰的左右最低点索引
first_peak_idx = peaks[0]
last_peak_idx = peaks[-1]

# 使用 peak_widths 获取左右基线索引
results_half = peak_widths(发帖数序列, peaks, rel_height=1.0)
left_base_first = results_half[2].astype(int)[0]
right_base_last = results_half[3].astype(int)[-1]

# 9.3 根据这些索引确定阶段边界的时间点
时间点_起始 = 活跃时段['小时'].iloc[0]  # 数据最早时间（作为起始阶段左边界）
时间点_第一谷底左侧 = 活跃时段['小时'].iloc[max(0, left_base_first - 1)]  # 第一个峰左谷底左边一点
时间点_第一谷底 = 活跃时段['小时'].iloc[left_base_first]  # 第一个峰左谷底
时间点_第一个峰 = 活跃时段['小时'].iloc[first_peak_idx]  # 第一个峰峰值时间
时间点_最后谷底 = 活跃时段['小时'].iloc[right_base_last]  # 最后一个峰右谷底
时间点_结束 = 活跃时段['小时'].iloc[-1]  # 数据最晚时间

# 9.4 划分阶段区间（用datetime）
阶段分界点 = [
    时间点_起始,
    时间点_第一谷底,
    时间点_第一个峰,
    时间点_最后谷底,
    时间点_结束
]

阶段名称 = ['起始阶段', '爆发阶段', '波动阶段', '长尾阶段']

# 分阶段提取每类用户的主导主题编号（前3）
from collections import Counter

阶段主题汇总 = []

for i in range(len(阶段分界点) - 1):
    t_start = 阶段分界点[i]
    t_end = 阶段分界点[i + 1]
    阶段 = 阶段名称[i]

    df_stage = df[(df['时间'] >= t_start) & (df['时间'] < t_end)]

    用户类型主题 = {}
    for user_type in df_stage['用户类型'].unique():
        df_user = df_stage[df_stage['用户类型'] == user_type]

        if not df_user.empty:
            # 统计出现最多的前3个主题编号
            # 排除 -1 的主题编号
            topic_counts = df_user[df_user['topic'] != -1]['topic'].value_counts()
            top_topic_ids = topic_counts.head(4).index.tolist()

            if top_topic_ids:
                用户类型主题[user_type] = top_topic_ids
            else:
                用户类型主题[user_type] = ["无有效主题"]
        else:
            用户类型主题[user_type] = ["无数据"]

    阶段主题汇总.append((阶段, 用户类型主题))


阶段主题汇总

#得到主导主题编号
主题汇总=[]
for _, (_, user_data) in enumerate(阶段主题汇总):
    for _, themes in user_data.items():
        主题汇总+=themes if themes != ["无有效主题"] else []
主题汇总=list(set(主题汇总))
for topic in 主题汇总:
    # 找出所有 topic 匹配的行
    matched_keywords = df[df['topic'] == topic]['keywords'].dropna().unique()
    
    # 打印标题和关键词列表
    print(f"\n【主题 {topic}】对应的关键词：")
    for kw in matched_keywords:
        print(kw)


#应用AI推导的主题名
df1=pd.read_excel("14少女2主导主题汇总.xlsx")
# 构造编号到主题含义的映射字典
theme_dict = dict(zip(df1['主题编号'], df1['主题含义']))
theme_dict

#做成表格
import pandas as pd

# 创建结果表格数据
result_data = []

# 获取所有用户类型
all_user_types = set()
for _, (_, user_data) in enumerate(阶段主题汇总):
    all_user_types.update(user_data.keys())

# 为每个用户类型创建一行数据
for user_type in sorted(all_user_types):
    row_data = {'用户类型': user_type}
    
    # 为每个阶段添加主题信息
    for stage_name, user_data in 阶段主题汇总:
        if user_type in user_data:
            topics = user_data[user_type]
            if topics == ['无有效主题'] or topics == ['无数据']:
                row_data[stage_name] = '无有效主题'
            else:
                # 将主题编号转换为主题名称，取前4个
                topic_names = []
                for topic_id in topics[:4]:  # 只取前4个主题
                    if topic_id in theme_dict:
                        topic_names.append(theme_dict[topic_id])
                    else:
                        topic_names.append(f'主题{topic_id}')
                row_data[stage_name] = '\n'.join(topic_names)
        else:
            row_data[stage_name] = '无数据'
    
    result_data.append(row_data)

# 创建DataFrame
result_df = pd.DataFrame(result_data)

# 设置用户类型为索引
result_df.set_index('用户类型', inplace=True)

print('\n=== 各阶段不同用户类型的四个主导主题 ===')
print(result_df)

# 创建桌面主题建模文件夹
import os
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
os.makedirs(desktop_path, exist_ok=True)

# 保存为Excel文件
excel_path = os.path.join(desktop_path, '各阶段用户类型主导主题表.xlsx')
result_df.to_excel(excel_path, encoding='utf-8-sig')
print(f'\n表格已保存为: {excel_path}')

# 创建更美观的表格显示
import matplotlib.pyplot as plt
from matplotlib import font_manager
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建简化版表格（只显示主要主题名称的前几个字）
simplified_data = []
for user_type in sorted(all_user_types):
    row_data = {'用户类型': user_type}
    
    for stage_name, user_data in 阶段主题汇总:
        if user_type in user_data:
            topics = user_data[user_type]
            if topics == ['无有效主题'] or topics == ['无数据']:
                row_data[stage_name] = '无有效主题'
            else:
                # 简化主题名称显示
                topic_names = []
                for topic_id in topics[:4]:  # 只取前4个主题
                    if topic_id in theme_dict:
                        # 取主题名称的前8个字符
                        short_name = theme_dict[topic_id][:8] + ('...' if len(theme_dict[topic_id]) > 8 else '')
                        topic_names.append(short_name)
                    else:
                        topic_names.append(f'主题{topic_id}')
                row_data[stage_name] = '\n'.join(topic_names)
        else:
            row_data[stage_name] = '无数据'
    
    simplified_data.append(row_data)

# 创建简化版DataFrame
simplified_df = pd.DataFrame(simplified_data)
simplified_df.set_index('用户类型', inplace=True)

print('\n=== 各阶段不同用户类型的主导主题（简化显示）===')
print(simplified_df)

# 创建可视化表格
fig, ax = plt.subplots(figsize=(16, 10))
ax.axis('tight')
ax.axis('off')

# 创建表格
table_data = []
table_data.append(['用户类型'] + list(simplified_df.columns))
for idx, row in simplified_df.iterrows():
    table_data.append([idx] + list(row.values))

table = ax.table(cellText=table_data, 
                cellLoc='center',
                loc='center',
                colWidths=[0.15, 0.2, 0.2, 0.2, 0.2])

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(9)
table.scale(1.2, 2)

# 设置标题行样式
for i in range(len(table_data[0])):
    table[(0, i)].set_facecolor('#4CAF50')
    table[(0, i)].set_text_props(weight='bold', color='white')

# 设置用户类型列样式
for i in range(1, len(table_data)):
    table[(i, 0)].set_facecolor('#E8F5E8')
    table[(i, 0)].set_text_props(weight='bold')

plt.title('各阶段不同用户类型的四个主导主题', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()

# 保存图片到桌面主题建模文件夹
png_path = os.path.join(desktop_path, '各阶段用户类型主导主题表.png')
plt.savefig(png_path, dpi=300, bbox_inches='tight')
plt.show()

print(f'\n可视化表格已保存为: {png_path}')