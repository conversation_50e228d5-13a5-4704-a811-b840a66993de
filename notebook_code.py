# 请将以下代码复制到您的Jupyter notebook新单元格中运行

import pandas as pd
import os
from collections import Counter

print("🚀 开始生成主导主题编号表格和阶段主题汇总表格...")

# ==================== 第一部分：生成主导主题编号与关键词表格 ====================

# 1. 提取所有主导主题编号
主导主题编号集合 = set()
for stage_name, user_data in 阶段主题汇总:
    for user_type, themes in user_data.items():
        if themes != ["无有效主题"] and themes != ["无数据"]:
            for theme in themes:
                if isinstance(theme, (int, float)) and theme != -1:
                    主导主题编号集合.add(int(theme))

主导主题编号列表 = sorted(list(主导主题编号集合))
print(f"📊 共发现 {len(主导主题编号列表)} 个主导主题编号")

# 2. 为每个主题编号获取对应的关键词
主导主题表格数据 = []

for topic_id in 主导主题编号列表:
    # 从df中获取该主题对应的关键词
    if 'topic' in df.columns and 'keywords' in df.columns:
        topic_keywords = df[df['topic'] == topic_id]['keywords'].dropna().unique()
        
        if len(topic_keywords) > 0:
            # 合并所有关键词
            all_keywords = []
            for kw_str in topic_keywords:
                if pd.notna(kw_str) and str(kw_str).strip():
                    keywords = [kw.strip() for kw in str(kw_str).split(',') if kw.strip()]
                    all_keywords.extend(keywords)
            
            # 统计关键词频次，取前10个最频繁的
            if all_keywords:
                keyword_counts = Counter(all_keywords)
                top_keywords = [kw for kw, count in keyword_counts.most_common(10)]
                keywords_str = ', '.join(top_keywords)
            else:
                keywords_str = "无关键词"
        else:
            keywords_str = "无关键词"
    else:
        keywords_str = "无关键词数据"
    
    主导主题表格数据.append({
        '主题编号': topic_id,
        '关键词': keywords_str
    })

# 创建主导主题表格DataFrame
主导主题表格 = pd.DataFrame(主导主题表格数据)

# ==================== 第二部分：生成阶段主题汇总表格 ====================

# 3. 创建阶段主题汇总详细表
阶段汇总表格数据 = []

for stage_name, user_data in 阶段主题汇总:
    for user_type, themes in user_data.items():
        if themes == ["无有效主题"] or themes == ["无数据"]:
            themes_str = "无有效主题"
            themes_count = 0
        else:
            # 过滤有效主题
            valid_themes = [t for t in themes if isinstance(t, (int, float)) and t != -1]
            themes_str = ', '.join([str(t) for t in valid_themes])
            themes_count = len(valid_themes)
        
        阶段汇总表格数据.append({
            '阶段': stage_name,
            '用户类型': user_type,
            '主题编号列表': themes_str,
            '主题数量': themes_count
        })

# 创建阶段汇总表格DataFrame
阶段汇总表格 = pd.DataFrame(阶段汇总表格数据)

# 4. 创建阶段汇总透视表（用户类型为行，阶段为列）
try:
    阶段透视表 = 阶段汇总表格.pivot_table(
        index='用户类型',
        columns='阶段',
        values='主题编号列表',
        aggfunc='first'
    )
except Exception as e:
    print(f"⚠️ 创建透视表时出错: {e}")
    阶段透视表 = pd.DataFrame()

# ==================== 第三部分：保存到桌面 ====================

# 5. 保存到桌面Excel文件
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')

# 保存主导主题编号表格
主题编号表格路径 = os.path.join(desktop_path, '主导主题编号关键词表.xlsx')
主导主题表格.to_excel(主题编号表格路径, index=False, encoding='utf-8-sig')

# 保存阶段主题汇总表格（包含多个工作表）
阶段汇总表格路径 = os.path.join(desktop_path, '阶段主题汇总表.xlsx')

with pd.ExcelWriter(阶段汇总表格路径, engine='openpyxl') as writer:
    # 工作表1：详细汇总表
    阶段汇总表格.to_excel(writer, sheet_name='阶段主题汇总详细', index=False)
    
    # 工作表2：透视表（如果创建成功）
    if not 阶段透视表.empty:
        阶段透视表.to_excel(writer, sheet_name='阶段主题透视表')
    
    # 工作表3：主导主题编号表格（作为参考）
    主导主题表格.to_excel(writer, sheet_name='主导主题编号表', index=False)

# ==================== 第四部分：显示结果 ====================

print("\n✅ 表格生成完成！")
print(f"📁 主导主题编号表格已保存到: {主题编号表格路径}")
print(f"📁 阶段主题汇总表格已保存到: {阶段汇总表格路径}")

print("\n=== 主导主题编号与关键词对应表 ===")
display(主导主题表格)

print(f"\n=== 阶段主题汇总详细表 (共{len(阶段汇总表格)}行) ===")
display(阶段汇总表格.head(10))  # 显示前10行

if not 阶段透视表.empty:
    print("\n=== 阶段主题透视表 ===")
    display(阶段透视表)

# 统计信息
print(f"\n📈 统计信息:")
print(f"   - 主导主题编号总数: {len(主导主题表格)}")
print(f"   - 阶段数量: {len(阶段汇总表格['阶段'].unique())}")
print(f"   - 用户类型数量: {len(阶段汇总表格['用户类型'].unique())}")
print(f"   - 各阶段: {list(阶段汇总表格['阶段'].unique())}")
print(f"   - 各用户类型: {list(阶段汇总表格['用户类型'].unique())}")

print("""
📋 生成的文件说明:
1. 主导主题编号关键词表.xlsx - 包含主题编号和对应关键词
2. 阶段主题汇总表.xlsx - 包含三个工作表:
   - 阶段主题汇总详细: 每个阶段每种用户类型的主题详情
   - 阶段主题透视表: 以用户类型为行、阶段为列的透视表
   - 主导主题编号表: 主题编号与关键词对应表（参考用）

🎉 所有表格已成功生成并保存到桌面！
""")
