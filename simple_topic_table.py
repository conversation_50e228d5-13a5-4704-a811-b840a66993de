# 请将以下代码复制到您的Jupyter notebook新单元格中运行

import pandas as pd
import os
from collections import Counter

print("🚀 生成主题关键词表格...")

# 提取所有主题汇总
主题汇总 = []
for _, (_, user_data) in enumerate(阶段主题汇总):
    for _, themes in user_data.items():
        主题汇总 += themes if themes != ["无有效主题"] else []
主题汇总 = list(set(主题汇总))

print(f"📊 共发现 {len(主题汇总)} 个主题")

# 创建主题关键词表格数据
主题关键词表格数据 = []

for topic in 主题汇总:
    # 找出所有 topic 匹配的行
    matched_keywords = df[df['topic'] == topic]['keywords'].dropna().unique()
    
    # 将所有关键词合并为一个字符串
    if len(matched_keywords) > 0:
        # 合并所有关键词
        all_keywords = []
        for kw_str in matched_keywords:
            if pd.notna(kw_str) and str(kw_str).strip():
                keywords = [kw.strip() for kw in str(kw_str).split(',') if kw.strip()]
                all_keywords.extend(keywords)
        
        # 去重并保持顺序
        unique_keywords = []
        seen = set()
        for kw in all_keywords:
            if kw not in seen:
                unique_keywords.append(kw)
                seen.add(kw)
        
        keywords_str = ', '.join(unique_keywords)
    else:
        keywords_str = "无关键词"
    
    主题关键词表格数据.append({
        '主题编号': topic,
        '关键词': keywords_str
    })

# 创建DataFrame并按主题编号排序
主题关键词表格 = pd.DataFrame(主题关键词表格数据)
主题关键词表格 = 主题关键词表格.sort_values('主题编号').reset_index(drop=True)

# 保存到桌面
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
excel_path = os.path.join(desktop_path, '主题关键词表.xlsx')
主题关键词表格.to_excel(excel_path, index=False, encoding='utf-8-sig')

print(f"📁 表格已保存到: {excel_path}")

print("\n=== 主题关键词表格 ===")
display(主题关键词表格)

print(f"\n📈 统计信息:")
print(f"   - 主题总数: {len(主题关键词表格)}")
print(f"   - 有关键词的主题数: {len(主题关键词表格[主题关键词表格['关键词'] != '无关键词'])}")

print("\n🎉 主题关键词表格已成功生成并保存到桌面！")
