# 请将以下代码复制到您的Jupyter notebook新单元格中运行

import pandas as pd
import os
from collections import Counter

print("🚀 生成主题关键词表格...")

# 提取所有主题汇总
主题汇总 = []
for _, (_, user_data) in enumerate(阶段主题汇总):
    for _, themes in user_data.items():
        主题汇总 += themes if themes != ["无有效主题"] else []
主题汇总 = list(set(主题汇总))

print(f"📊 共发现 {len(主题汇总)} 个主题")

# 创建主题关键词表格数据
主题关键词表格数据 = []

for topic in 主题汇总:
    # 找出所有 topic 匹配的行
    matched_keywords = df[df['topic'] == topic]['keywords'].dropna().unique()
    
    # 将所有关键词合并为一个字符串
    if len(matched_keywords) > 0:
        # 合并所有关键词
        all_keywords = []
        for kw_str in matched_keywords:
            if pd.notna(kw_str) and str(kw_str).strip():
                keywords = [kw.strip() for kw in str(kw_str).split(',') if kw.strip()]
                all_keywords.extend(keywords)
        
        # 去重并保持顺序
        unique_keywords = []
        seen = set()
        for kw in all_keywords:
            if kw not in seen:
                unique_keywords.append(kw)
                seen.add(kw)
        
        keywords_str = ', '.join(unique_keywords)
    else:
        keywords_str = "无关键词"
    
    主题关键词表格数据.append({
        '主题编号': topic,
        '关键词': keywords_str
    })

# 基于关键词推导主题名的AI函数
def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if not keywords_str or keywords_str == "无关键词":
        return "未知主题"

    keywords = [kw.strip().lower() for kw in keywords_str.split(',') if kw.strip()]

    # 主题推导规则
    rules = [
        # 情绪表达类
        (['转发', 'repost', '哈哈哈', '别跑', '轉發'], "信息转发与传播"),
        (['打得', '打死', '杀死', '打打', '过份', '找死', '迟早'], "暴力情绪表达"),
        (['流泪', '祈祷', '发怒', '捂脸', '灵光', '一闪'], "情绪反应与表达"),

        # 社会问题类
        (['社会', '老百姓', '黑社会', '百态', '报复'], "社会问题讨论"),
        (['生路', '为难', '生活', '何必', '别人', '不易'], "民生困境关注"),
        (['法律', '违法', '犯法', '文明执法', '依规'], "法律执法讨论"),

        # 冲突事件类
        (['冲突', '发生冲突', '大喊', '不活', '铁棍', '城管'], "城管执法冲突"),
        (['并持', '男子', '猛追', '另有隐情'], "冲突事件细节"),
        (['打架', '炸锅', '纠纷', '后续', '触犯'], "冲突后续处理"),

        # 执法相关类
        (['执法人员', '队员', '天河区', '劝导', '环境', '摆卖'], "执法管理工作"),
        (['伤情', '接报', '到场', '警情', '处置'], "警方处置程序"),
        (['记者', '无证', '事发', '骨折', '获悉', '核实'], "事件调查报道"),

        # 网络传播类
        (['网传', 'http', 'cn', '广州', '浏览器'], "网络传播信息"),
        (['账号', '作品', '修改', '视频', '超级'], "网络平台内容"),
        (['百态', '热点', '话题', '社会'], "社会热点话题"),

        # 地摊经营类
        (['小商贩', '收摊', '规矩', '地摊', '赶尽杀绝'], "地摊经营问题"),
        (['摆地摊', '地痞流氓', '地方', '鸡毛'], "摊贩管理冲突"),
        (['游商', '目击者', '劝阻', '带走', '激动'], "流动商贩管理"),

        # 官方处理类
        (['贪官', '几千万', '死刑', '百姓', '绝路', '刑拘'], "反腐败讨论"),
        (['公安机关', '等待', '触犯', '调查', '部门'], "官方调查处理"),
        (['白衣', '工作人员', '侦办', '新闻', '图源'], "官方通报信息"),

        # 其他特殊类
        (['禅语', '红色', '编辑', '祝福语', '灵验'], "网络文化传播"),
        (['货箱', '拦阻', '放手', '棍子', '名城'], "具体冲突场景")
    ]

    # 匹配规则
    for rule_keywords, topic_name in rules:
        if any(kw in keywords for kw in rule_keywords):
            return topic_name

    # 如果没有匹配的规则，基于主要关键词生成
    if len(keywords) > 0:
        main_keyword = keywords[0]
        if any(word in main_keyword for word in ['法', '执', '管']):
            return "执法相关讨论"
        elif any(word in main_keyword for word in ['社会', '百姓', '民']):
            return "社会民生话题"
        elif any(word in main_keyword for word in ['网', '传', '视频']):
            return "网络传播内容"
        else:
            return f"{main_keyword}相关讨论"

    return "综合性讨论"

# 为每个主题添加AI推导的主题名
for item in 主题关键词表格数据:
    item['AI推导主题名'] = infer_topic_name(item['关键词'])

# 创建DataFrame并按主题编号排序
主题关键词表格 = pd.DataFrame(主题关键词表格数据)
主题关键词表格 = 主题关键词表格.sort_values('主题编号').reset_index(drop=True)

# 保存到桌面
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
excel_path = os.path.join(desktop_path, '主题关键词表.xlsx')
主题关键词表格.to_excel(excel_path, index=False, encoding='utf-8-sig')

print(f"📁 表格已保存到: {excel_path}")

print("\n=== 主题关键词表格 ===")
print("主题编号 | 关键词 | AI推导主题名")
print("-" * 80)
for i, row in 主题关键词表格.iterrows():
    topic_num = row['主题编号']
    keywords = row['关键词'][:50] + "..." if len(row['关键词']) > 50 else row['关键词']
    topic_name = row['AI推导主题名']
    print(f"{topic_num:6d} | {keywords:50s} | {topic_name}")

print(f"\n📈 统计信息:")
print(f"   - 主题总数: {len(主题关键词表格)}")
print(f"   - 有关键词的主题数: {len(主题关键词表格[主题关键词表格['关键词'] != '无关键词'])}")

print("\n🎉 主题关键词表格已成功生成并保存到桌面！")
